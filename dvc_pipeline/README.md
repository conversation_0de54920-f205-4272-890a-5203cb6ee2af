# 动态公司池投资组合回测 DVC 管道

这个项目使用 DVC (Data Version Control) 来管理数据处理和机器学习管道，实现可重现的动态公司池投资组合回测。

## 📁 项目结构

```
dvc_pipeline/
├── data/
│   ├── raw/                    # 原始数据
│   │   └── SP500 Hsitorical Data.csv
│   └── processed/              # 处理后的数据
│       ├── SP500_Historical_Data_Cleaned.csv
│       └── SP500_Historical_Data_Reordered.csv
├── scripts/                    # 处理脚本
│   ├── analyze_delisted_stocks.py
│   ├── reorder_columns_by_obs.py
│   ├── dynamic_universe_backtest.py
│   └── parametrized_backtest.py
├── results/                    # 结果输出
│   ├── backtest_metrics.json
│   ├── detailed_results.json
│   └── performance_plot.json
├── dvc.yaml                    # DVC 管道定义
├── params.yaml                 # 参数配置
└── README.md                   # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install dvc pyyaml
```

### 2. 运行完整管道
```bash
# 运行所有阶段
dvc repro

# 或者运行特定阶段
dvc repro clean_data
dvc repro reorder_columns
dvc repro backtest
```

### 3. 查看结果
```bash
# 查看指标
dvc metrics show

# 查看管道状态
dvc status

# 查看管道图
dvc dag
```

## 📊 管道阶段

### 1. clean_data
- **输入**: `data/raw/SP500 Hsitorical Data.csv`
- **输出**: `data/processed/SP500_Historical_Data_Cleaned.csv`
- **功能**: 清理SP500历史数据，移除已退市股票
- **参数**: 
  - `recent_days`: 252 (分析最近多少个交易日)
  - `valid_ratio_threshold`: 0.1 (有效数据比例阈值)

### 2. reorder_columns
- **输入**: `data/processed/SP500_Historical_Data_Cleaned.csv`
- **输出**: `data/processed/SP500_Historical_Data_Reordered.csv`
- **功能**: 按观测数量重新排序列，时间列在最前
- **参数**:
  - `time_column_first`: true
  - `sort_by_observations`: true

### 3. backtest
- **输入**: `data/processed/SP500_Historical_Data_Reordered.csv`
- **输出**: 
  - `results/backtest_metrics.json`
  - `results/detailed_results.json`
  - `results/performance_plot.json`
- **功能**: 运行参数化的动态公司池投资组合回测
- **参数**:
  - `min_history_days`: 252
  - `data_quality_threshold`: 0.7
  - `equal_weight.rebalance_freq`: 63

## ⚙️ 参数配置

所有参数都在 `params.yaml` 文件中定义：

```yaml
# 数据处理参数
data_processing:
  delisting_detection:
    recent_days: 252
    valid_ratio_threshold: 0.1
  column_reordering:
    time_column_first: true
    sort_by_observations: true

# 回测参数
backtesting:
  min_history_days: 252
  data_quality_threshold: 0.7
  equal_weight:
    rebalance_freq: 63
  momentum:
    lookback_days: 63
    top_n: 30
    rebalance_freq: 21

# 输出设置
output:
  save_detailed_logs: true
  generate_plots: true
  export_metrics: true
```

## 📈 结果输出

### 指标文件 (backtest_metrics.json)
```json
{
  "total_return": 8.3306,
  "annual_return": 0.0533,
  "annual_volatility": 0.1760,
  "sharpe_ratio": 0.303,
  "avg_universe_size": 356.8,
  "start_date": "31dec1980",
  "end_date": "29dec2023"
}
```

### 详细结果 (detailed_results.json)
包含完整的策略参数、性能数据和时间序列结果。

### 绘图数据 (performance_plot.json)
用于生成性能图表的时间序列数据。

## 🔄 实验管理

### 修改参数并重新运行
```bash
# 修改 params.yaml 中的参数
vim params.yaml

# 重新运行管道
dvc repro

# 比较结果
dvc metrics diff
```

### 管道可视化
```bash
# 生成管道图
dvc dag

# 查看依赖关系
dvc dag --dot | dot -Tpng -o pipeline.png
```

## 🎯 核心特性

1. **可重现性**: 所有数据处理步骤都被DVC跟踪
2. **参数化**: 通过 `params.yaml` 轻松调整实验参数
3. **增量执行**: 只重新运行发生变化的阶段
4. **结果跟踪**: 自动跟踪和比较实验结果
5. **可视化**: 管道依赖关系和结果的可视化

## 📝 使用示例

```bash
# 初始运行
dvc repro

# 修改回测参数
echo "backtesting.equal_weight.rebalance_freq: 21" >> params.yaml

# 重新运行回测阶段
dvc repro backtest

# 查看指标变化
dvc metrics diff

# 查看管道状态
dvc status
```

## 🔧 扩展功能

- 添加新的数据处理阶段
- 实现多种投资策略
- 集成更多性能指标
- 添加数据验证步骤
- 实现模型训练和评估

---

*这个DVC管道提供了一个完整的、可重现的量化投资研究框架。*
