#!/usr/bin/env python3
"""
参数化的动态公司池投资组合回测脚本
支持DVC参数和结果输出
"""

import csv
import json
import yaml
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict
import math

class ParametrizedBacktest:
    """参数化的动态公司池回测类"""
    
    def __init__(self, data_file, params_file="params.yaml"):
        """
        初始化回测框架
        
        Args:
            data_file: CSV数据文件路径
            params_file: 参数配置文件路径
        """
        self.data_file = data_file
        self.load_params(params_file)
        self.data = None
        self.dates = []
        self.stock_columns = []
        
        self.load_data()
    
    def load_params(self, params_file):
        """加载参数配置"""
        try:
            with open(params_file, 'r', encoding='utf-8') as f:
                self.params = yaml.safe_load(f)
        except FileNotFoundError:
            print(f"参数文件 {params_file} 未找到，使用默认参数")
            self.params = self.get_default_params()
    
    def get_default_params(self):
        """获取默认参数"""
        return {
            'backtesting': {
                'min_history_days': 252,
                'data_quality_threshold': 0.7,
                'equal_weight': {'rebalance_freq': 63},
                'momentum': {'lookback_days': 63, 'top_n': 30, 'rebalance_freq': 21}
            },
            'output': {
                'save_detailed_logs': True,
                'generate_plots': True,
                'export_metrics': True
            }
        }
    
    def load_data(self):
        """加载数据并预处理"""
        print("正在加载数据...")
        
        with open(self.data_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            rows = list(reader)
        
        self.dates = [row[0] for row in rows]
        self.stock_columns = [col for col in headers[1:] if col.startswith('RET')]
        
        self.data = {}
        for i, col in enumerate(headers):
            if col == 'date':
                self.data[col] = [row[0] for row in rows]
            else:
                self.data[col] = [self._safe_float(row[i]) if i < len(row) else None 
                                 for row in rows]
        
        print(f"数据加载完成: {len(self.dates)}个交易日, {len(self.stock_columns)}只股票")
    
    def _safe_float(self, value):
        """安全转换浮点数"""
        if not value or value.strip() in ['', '.b', 'b', '.', 'nan', 'NaN']:
            return None
        try:
            return float(value)
        except ValueError:
            return None
    
    def get_available_stocks(self, current_idx, lookback_days=None):
        """获取在指定时点可用的股票池"""
        if lookback_days is None:
            lookback_days = self.params['backtesting']['min_history_days']
        
        threshold = self.params['backtesting']['data_quality_threshold']
        available_stocks = []
        start_idx = max(0, current_idx - lookback_days)
        
        for stock in self.stock_columns:
            values = self.data[stock][start_idx:current_idx+1]
            valid_values = [v for v in values if v is not None]
            
            if (len(valid_values) >= lookback_days * threshold and 
                self.data[stock][current_idx] is not None):
                available_stocks.append(stock)
        
        return available_stocks
    
    def equal_weight_strategy(self):
        """等权重投资策略"""
        rebalance_freq = self.params['backtesting']['equal_weight']['rebalance_freq']
        min_history = self.params['backtesting']['min_history_days']
        
        print(f"开始等权重策略回测，再平衡频率: {rebalance_freq}天")
        
        portfolio_returns = []
        universe_sizes = []
        rebalance_dates = []
        
        start_idx = min_history
        current_portfolio = {}
        
        for i in range(start_idx, len(self.dates)):
            if i == start_idx or (i - start_idx) % rebalance_freq == 0:
                available_stocks = self.get_available_stocks(i)
                universe_sizes.append(len(available_stocks))
                rebalance_dates.append(self.dates[i])
                
                if len(available_stocks) > 0:
                    weight = 1.0 / len(available_stocks)
                    current_portfolio = {stock: weight for stock in available_stocks}
                else:
                    current_portfolio = {}
            
            # 计算当期组合收益
            if i > start_idx and len(current_portfolio) > 0:
                period_return = 0.0
                valid_stocks = 0
                
                for stock, weight in current_portfolio.items():
                    stock_return = self.data[stock][i]
                    if stock_return is not None:
                        period_return += weight * stock_return
                        valid_stocks += 1
                
                portfolio_returns.append(period_return if valid_stocks > 0 else 0.0)
            else:
                portfolio_returns.append(0.0)
        
        # 计算累积收益
        cumulative_returns = []
        cum_ret = 1.0
        for ret in portfolio_returns:
            cum_ret *= (1 + ret)
            cumulative_returns.append(cum_ret)
        
        return {
            'strategy': 'equal_weight',
            'dates': self.dates[start_idx:],
            'returns': portfolio_returns,
            'cumulative_returns': cumulative_returns,
            'universe_sizes': universe_sizes,
            'rebalance_dates': rebalance_dates
        }
    
    def calculate_metrics(self, results):
        """计算策略表现指标"""
        returns = results['returns']
        
        # 基础统计
        total_return = results['cumulative_returns'][-1] - 1.0
        n_years = len(returns) / 252
        annual_return = (1 + total_return) ** (1/n_years) - 1
        
        # 波动率
        returns_array = [r for r in returns if r is not None]
        mean_return = sum(returns_array) / len(returns_array)
        variance = sum((r - mean_return) ** 2 for r in returns_array) / len(returns_array)
        annual_volatility = math.sqrt(variance * 252)
        
        # 夏普比率
        sharpe_ratio = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        # 平均投资池大小
        avg_universe_size = sum(results['universe_sizes']) / len(results['universe_sizes'])
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'avg_universe_size': avg_universe_size,
            'start_date': results['dates'][0],
            'end_date': results['dates'][-1],
            'n_observations': len(returns)
        }
    
    def save_results(self, results, output_dir="results"):
        """保存回测结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存指标
        metrics = self.calculate_metrics(results)
        with open(f"{output_dir}/backtest_metrics.json", 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        
        # 保存详细结果
        if self.params['output']['save_detailed_logs']:
            detailed_results = {
                'strategy': results['strategy'],
                'parameters': self.params,
                'metrics': metrics,
                'performance_data': {
                    'dates': results['dates'],
                    'returns': results['returns'],
                    'cumulative_returns': results['cumulative_returns'],
                    'universe_sizes': results['universe_sizes']
                }
            }
            
            with open(f"{output_dir}/detailed_results.json", 'w', encoding='utf-8') as f:
                json.dump(detailed_results, f, indent=2, ensure_ascii=False)
        
        # 生成绘图数据
        if self.params['output']['generate_plots']:
            plot_data = []
            for i, date in enumerate(results['dates']):
                plot_data.append({
                    'date': date,
                    'cumulative_return': results['cumulative_returns'][i],
                    'universe_size': results['universe_sizes'][i // self.params['backtesting']['equal_weight']['rebalance_freq']] if i % self.params['backtesting']['equal_weight']['rebalance_freq'] == 0 else None
                })
            
            with open(f"{output_dir}/performance_plot.json", 'w', encoding='utf-8') as f:
                json.dump(plot_data, f, indent=2, ensure_ascii=False)
        
        return metrics

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 parametrized_backtest.py <data_file> [params_file]")
        sys.exit(1)
    
    data_file = sys.argv[1]
    params_file = sys.argv[2] if len(sys.argv) > 2 else "params.yaml"
    
    # 初始化回测框架
    backtest = ParametrizedBacktest(data_file, params_file)
    
    print("\n" + "="*60)
    print("参数化动态公司池投资组合回测")
    print("="*60)
    
    # 运行等权重策略
    eq_results = backtest.equal_weight_strategy()
    metrics = backtest.save_results(eq_results)
    
    # 打印结果摘要
    print(f"\n=== 等权重策略表现摘要 ===")
    print(f"回测期间: {metrics['start_date']} 到 {metrics['end_date']}")
    print(f"总收益率: {metrics['total_return']:.2%}")
    print(f"年化收益率: {metrics['annual_return']:.2%}")
    print(f"年化波动率: {metrics['annual_volatility']:.2%}")
    print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
    print(f"平均投资池大小: {metrics['avg_universe_size']:.1f}只股票")
    
    print(f"\n结果已保存到 results/ 目录")

if __name__ == "__main__":
    main()
