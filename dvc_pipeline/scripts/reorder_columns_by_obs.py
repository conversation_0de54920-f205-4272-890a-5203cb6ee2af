#!/usr/bin/env python3
"""
重新排列CSV文件的列顺序，按照每个股票的有效观测值数量从多到少排序
"""

import csv
import sys
from collections import defaultdict

def is_valid_value(value):
    """判断一个值是否为有效的股票收益率数据"""
    if not value or value.strip() == '':
        return False
    if value.strip() in ['.b', 'b', '.', 'nan', 'NaN', 'NULL']:
        return False
    try:
        float(value)
        return True
    except ValueError:
        return False

def reorder_columns_by_observations(input_file, output_file):
    """
    重新排列CSV文件的列顺序，按照有效观测值数量排序
    
    Args:
        input_file: 输入的CSV文件路径
        output_file: 输出的重新排序后CSV文件路径
    """
    
    print("正在读取CSV文件...")
    try:
        # 读取CSV文件
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取标题行
            
            # 将所有数据读入内存
            all_rows = list(reader)
        
        total_rows = len(all_rows)
        print(f"数据行数: {total_rows}")
        print(f"列数: {len(headers)}")
        
        # 找到日期列的索引
        date_col_idx = None
        for i, header in enumerate(headers):
            if header.lower() in ['date', 'time', '日期', '时间']:
                date_col_idx = i
                break
        
        if date_col_idx is None:
            # 假设第一列是日期列
            date_col_idx = 0
            print("未找到明确的日期列，假设第一列为日期列")
        else:
            print(f"找到日期列: {headers[date_col_idx]} (索引 {date_col_idx})")
        
        # 获取所有股票列的索引（除了日期列）
        stock_columns = []
        for i, header in enumerate(headers):
            if i != date_col_idx:
                stock_columns.append((i, header))
        
        print(f"股票列数: {len(stock_columns)}")
        
        # 统计每个股票列的有效观测值数量
        print("正在统计每个股票的有效观测值数量...")
        stock_obs_counts = []
        
        for col_idx, col_name in stock_columns:
            valid_count = 0
            for row in all_rows:
                if col_idx < len(row) and is_valid_value(row[col_idx]):
                    valid_count += 1
            
            stock_obs_counts.append({
                'index': col_idx,
                'name': col_name,
                'valid_count': valid_count,
                'valid_ratio': valid_count / total_rows if total_rows > 0 else 0
            })
        
        # 按有效观测值数量从多到少排序
        stock_obs_counts.sort(key=lambda x: x['valid_count'], reverse=True)
        
        print(f"\n有效观测值统计 (前10名):")
        for i, stock in enumerate(stock_obs_counts[:10]):
            stock_symbol = stock['name'][3:] if stock['name'].startswith('RET') else stock['name']
            print(f"  {i+1:2d}. {stock_symbol:8s}: {stock['valid_count']:5d}/{total_rows} ({stock['valid_ratio']:.1%})")
        
        print(f"\n有效观测值统计 (后10名):")
        for i, stock in enumerate(stock_obs_counts[-10:]):
            rank = len(stock_obs_counts) - 9 + i
            stock_symbol = stock['name'][3:] if stock['name'].startswith('RET') else stock['name']
            print(f"  {rank:2d}. {stock_symbol:8s}: {stock['valid_count']:5d}/{total_rows} ({stock['valid_ratio']:.1%})")
        
        # 创建新的列顺序：日期列 + 按观测值数量排序的股票列
        new_column_order = [date_col_idx]  # 日期列始终在第一位
        for stock in stock_obs_counts:
            new_column_order.append(stock['index'])
        
        print(f"\n正在创建重新排序的CSV文件...")
        
        # 保存重新排序后的数据
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入新的标题行
            new_headers = [headers[i] for i in new_column_order]
            writer.writerow(new_headers)
            
            # 写入数据行
            for row in all_rows:
                new_row = []
                for col_idx in new_column_order:
                    if col_idx < len(row):
                        new_row.append(row[col_idx])
                    else:
                        new_row.append('')
                writer.writerow(new_row)
        
        print(f"重新排序的数据已保存到: {output_file}")
        
        # 保存排序统计信息
        stats_file = output_file.replace('.csv', '_column_stats.txt')
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("股票列按有效观测值数量排序统计\n")
            f.write("=" * 50 + "\n")
            f.write(f"总数据行数: {total_rows}\n")
            f.write(f"股票列数: {len(stock_obs_counts)}\n\n")
            
            f.write("排序结果 (按有效观测值数量从多到少):\n")
            f.write("-" * 50 + "\n")
            f.write(f"{'排名':<4} {'股票代码':<10} {'有效观测值':<10} {'比例':<8} {'列名'}\n")
            f.write("-" * 50 + "\n")
            
            for i, stock in enumerate(stock_obs_counts):
                stock_symbol = stock['name'][3:] if stock['name'].startswith('RET') else stock['name']
                f.write(f"{i+1:<4} {stock_symbol:<10} {stock['valid_count']:<10} {stock['valid_ratio']:.1%} {stock['name']}\n")
        
        print(f"列排序统计信息已保存到: {stats_file}")
        
        return {
            'total_columns': len(headers),
            'stock_columns': len(stock_obs_counts),
            'reordered_file': output_file,
            'stats_file': stats_file
        }
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3:
        print("用法: python3 reorder_columns_by_obs.py <input_file> <output_file>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    print("SP500历史数据列重排序工具")
    print("=" * 40)

    result = reorder_columns_by_observations(input_file, output_file)

    if result:
        print(f"\n处理完成!")
        print(f"总列数: {result['total_columns']}")
        print(f"股票列数: {result['stock_columns']}")
        print(f"输出文件: {result['reordered_file']}")
        print(f"统计文件: {result['stats_file']}")
    else:
        print("处理失败!")
        sys.exit(1)
