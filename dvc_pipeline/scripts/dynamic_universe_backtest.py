#!/usr/bin/env python3
"""
动态公司池投资组合回测框架
处理公司进入/退出的简洁版本
"""

import csv
from datetime import datetime, timedelta
from collections import defaultdict
import math

class DynamicUniverseBacktest:
    """
    动态公司池回测类
    核心思想：在每个时点只使用当时可用的股票
    """
    
    def __init__(self, data_file, min_history_days=252):
        """
        初始化回测框架
        
        Args:
            data_file: CSV数据文件路径
            min_history_days: 股票进入投资池所需的最小历史数据天数
        """
        self.data_file = data_file
        self.min_history_days = min_history_days
        self.data = None
        self.dates = []
        self.stock_columns = []
        
        self.load_data()
    
    def load_data(self):
        """加载数据并预处理"""
        print("正在加载数据...")
        
        # 使用标准库读取CSV（避免pandas依赖）
        with open(self.data_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            rows = list(reader)
        
        # 提取日期和股票列
        self.dates = [row[0] for row in rows]
        self.stock_columns = [col for col in headers[1:] if col.startswith('RET')]
        
        # 转换为字典格式便于处理
        self.data = {}
        for i, col in enumerate(headers):
            if col == 'date':
                self.data[col] = [row[0] for row in rows]
            else:
                self.data[col] = [self._safe_float(row[i]) if i < len(row) else None 
                                 for row in rows]
        
        print(f"数据加载完成: {len(self.dates)}个交易日, {len(self.stock_columns)}只股票")
    
    def _safe_float(self, value):
        """安全转换为浮点数"""
        if not value or value.strip() in ['', '.b', 'b', '.', 'nan', 'NaN']:
            return None
        try:
            return float(value)
        except ValueError:
            return None
    
    def get_available_stocks(self, current_idx, lookback_days=None):
        """
        获取在指定时点可用的股票池
        
        Args:
            current_idx: 当前时点的索引
            lookback_days: 回看天数，用于检查数据质量
        
        Returns:
            list: 可用股票列表
        """
        if lookback_days is None:
            lookback_days = self.min_history_days
        
        available_stocks = []
        start_idx = max(0, current_idx - lookback_days)
        
        for stock in self.stock_columns:
            # 检查该股票在回看期内的数据质量
            values = self.data[stock][start_idx:current_idx+1]
            valid_values = [v for v in values if v is not None]
            
            # 要求：1) 有足够的历史数据 2) 当前时点有数据
            if (len(valid_values) >= lookback_days * 0.7 and  # 70%的数据可用
                self.data[stock][current_idx] is not None):
                available_stocks.append(stock)
        
        return available_stocks
    
    def calculate_returns(self, stocks, start_idx, end_idx):
        """
        计算指定股票在指定期间的收益率
        
        Args:
            stocks: 股票列表
            start_idx: 开始索引
            end_idx: 结束索引
        
        Returns:
            dict: {股票: 收益率}
        """
        returns = {}
        for stock in stocks:
            start_val = self.data[stock][start_idx]
            end_val = self.data[stock][end_idx]
            
            if start_val is not None and end_val is not None and start_val != 0:
                returns[stock] = end_val  # 这里假设数据已经是收益率
            else:
                returns[stock] = 0.0
        
        return returns
    
    def equal_weight_strategy(self, rebalance_freq=21):
        """
        等权重投资策略示例
        
        Args:
            rebalance_freq: 再平衡频率（交易日）
        
        Returns:
            dict: 回测结果
        """
        print(f"开始等权重策略回测，再平衡频率: {rebalance_freq}天")
        
        portfolio_values = []
        portfolio_returns = []
        universe_sizes = []
        rebalance_dates = []
        
        # 从有足够历史数据的时点开始
        start_idx = self.min_history_days
        
        for i in range(start_idx, len(self.dates)):
            # 检查是否需要再平衡
            if i == start_idx or (i - start_idx) % rebalance_freq == 0:
                # 获取当前可用股票池
                available_stocks = self.get_available_stocks(i)
                universe_sizes.append(len(available_stocks))
                rebalance_dates.append(self.dates[i])
                
                print(f"日期: {self.dates[i]}, 可用股票: {len(available_stocks)}只")
                
                if len(available_stocks) == 0:
                    portfolio_returns.append(0.0)
                    continue
                
                # 等权重分配
                weight = 1.0 / len(available_stocks)
                current_portfolio = {stock: weight for stock in available_stocks}
            
            # 计算当期组合收益
            if i > start_idx and len(current_portfolio) > 0:
                period_return = 0.0
                valid_stocks = 0
                
                for stock, weight in current_portfolio.items():
                    stock_return = self.data[stock][i]
                    if stock_return is not None:
                        period_return += weight * stock_return
                        valid_stocks += 1
                
                # 如果有效股票数量不足，按比例调整
                if valid_stocks > 0:
                    portfolio_returns.append(period_return)
                else:
                    portfolio_returns.append(0.0)
            else:
                portfolio_returns.append(0.0)
        
        # 计算累积收益
        cumulative_returns = []
        cum_ret = 1.0
        for ret in portfolio_returns:
            cum_ret *= (1 + ret)
            cumulative_returns.append(cum_ret)
        
        return {
            'dates': self.dates[start_idx:],
            'returns': portfolio_returns,
            'cumulative_returns': cumulative_returns,
            'universe_sizes': universe_sizes,
            'rebalance_dates': rebalance_dates
        }
    
    def momentum_strategy(self, lookback_days=63, top_n=20, rebalance_freq=21):
        """
        动量策略示例：选择过去N天表现最好的股票
        
        Args:
            lookback_days: 动量计算回看期
            top_n: 选择前N只股票
            rebalance_freq: 再平衡频率
        
        Returns:
            dict: 回测结果
        """
        print(f"开始动量策略回测，回看期: {lookback_days}天, 选择前{top_n}只股票")
        
        portfolio_returns = []
        universe_sizes = []
        selected_stocks_history = []
        
        start_idx = self.min_history_days + lookback_days
        
        for i in range(start_idx, len(self.dates)):
            if i == start_idx or (i - start_idx) % rebalance_freq == 0:
                # 获取可用股票池
                available_stocks = self.get_available_stocks(i)
                universe_sizes.append(len(available_stocks))
                
                if len(available_stocks) == 0:
                    current_portfolio = {}
                    selected_stocks_history.append([])
                    continue
                
                # 计算动量得分（过去lookback_days的累积收益）
                momentum_scores = {}
                for stock in available_stocks:
                    returns = []
                    for j in range(i - lookback_days, i):
                        ret = self.data[stock][j]
                        if ret is not None:
                            returns.append(ret)
                    
                    if len(returns) >= lookback_days * 0.7:  # 至少70%的数据
                        momentum_scores[stock] = sum(returns)
                
                # 选择动量得分最高的股票
                sorted_stocks = sorted(momentum_scores.items(), 
                                     key=lambda x: x[1], reverse=True)
                selected_stocks = [stock for stock, score in sorted_stocks[:top_n]]
                selected_stocks_history.append(selected_stocks)
                
                # 等权重分配给选中的股票
                if len(selected_stocks) > 0:
                    weight = 1.0 / len(selected_stocks)
                    current_portfolio = {stock: weight for stock in selected_stocks}
                else:
                    current_portfolio = {}
                
                print(f"日期: {self.dates[i]}, 可用股票: {len(available_stocks)}只, "
                      f"选中: {len(selected_stocks)}只")
            
            # 计算组合收益
            if len(current_portfolio) > 0:
                period_return = 0.0
                for stock, weight in current_portfolio.items():
                    stock_return = self.data[stock][i]
                    if stock_return is not None:
                        period_return += weight * stock_return
                portfolio_returns.append(period_return)
            else:
                portfolio_returns.append(0.0)
        
        return {
            'dates': self.dates[start_idx:],
            'returns': portfolio_returns,
            'universe_sizes': universe_sizes,
            'selected_stocks_history': selected_stocks_history
        }
    
    def print_performance_summary(self, results, strategy_name):
        """打印策略表现摘要"""
        returns = results['returns']
        
        # 基本统计
        total_return = (1 + sum(returns)) - 1
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1

        # 计算波动率（标准差）
        if len(returns) > 1:
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / (len(returns) - 1)
            volatility = math.sqrt(variance) * math.sqrt(252)
        else:
            volatility = 0

        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        
        print(f"\n=== {strategy_name} 表现摘要 ===")
        print(f"回测期间: {results['dates'][0]} 到 {results['dates'][-1]}")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annualized_return:.2%}")
        print(f"年化波动率: {volatility:.2%}")
        print(f"夏普比率: {sharpe_ratio:.3f}")
        avg_universe_size = sum(results['universe_sizes']) / len(results['universe_sizes'])
        print(f"平均投资池大小: {avg_universe_size:.1f}只股票")

def main():
    """主函数：运行回测示例"""
    
    # 初始化回测框架
    backtest = DynamicUniverseBacktest("SP500_Historical_Data_Reordered.csv")
    
    print("\n" + "="*60)
    print("动态公司池投资组合回测示例")
    print("="*60)
    
    # 策略1：等权重策略
    eq_results = backtest.equal_weight_strategy(rebalance_freq=63)  # 季度再平衡
    backtest.print_performance_summary(eq_results, "等权重策略")
    
    # 策略2：动量策略
    mom_results = backtest.momentum_strategy(lookback_days=63, top_n=30, rebalance_freq=21)
    backtest.print_performance_summary(mom_results, "动量策略")
    
    print(f"\n=== 关键设计思路 ===")
    print("1. 动态股票池：每个时点只使用当时可用的股票")
    print("2. 数据质量控制：要求足够的历史数据和当前数据可用")
    print("3. 生存偏差处理：自然处理公司进入/退出")
    print("4. 灵活的策略框架：可以轻松添加新的选股和权重策略")

if __name__ == "__main__":
    main()
