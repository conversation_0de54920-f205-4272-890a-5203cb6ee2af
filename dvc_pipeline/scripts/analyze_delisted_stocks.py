#!/usr/bin/env python3
"""
分析SP500历史数据，识别基于最近一年数据可能已退市的公司
"""

import csv
import sys
from collections import defaultdict

def is_valid_value(value):
    """判断一个值是否为有效的股票收益率数据"""
    if not value or value.strip() == '':
        return False
    if value.strip() in ['.b', 'b', '.', 'nan', 'NaN', 'NULL']:
        return False
    try:
        float(value)
        return True
    except ValueError:
        return False

def analyze_delisted_stocks(csv_file, output_file=None):
    """
    分析CSV文件，识别在最近一年中没有有效数据的股票（可能已退市）

    Args:
        csv_file: 输入的CSV文件路径
        output_file: 输出的清理后CSV文件路径（可选）
    """

    print("正在读取CSV文件...")
    try:
        # 读取CSV文件
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取标题行

            # 将所有数据读入内存
            all_rows = list(reader)

        total_rows = len(all_rows)
        print(f"数据行数: {total_rows}")
        print(f"列数: {len(headers)}")

        if total_rows > 0:
            print(f"日期范围: {all_rows[0][0]} 到 {all_rows[-1][0]}")

        # 获取最近一年的数据（最后252个交易日，约一年）
        recent_days = min(252, total_rows)
        recent_data = all_rows[-recent_days:]

        print(f"分析最近 {recent_days} 个交易日的数据...")
        if recent_data:
            print(f"分析期间: {recent_data[0][0]} 到 {recent_data[-1][0]}")

        # 获取所有股票列的索引（以RET开头的列）
        stock_columns = []
        for i, header in enumerate(headers):
            if header.startswith('RET'):
                stock_columns.append((i, header))

        print(f"总共有 {len(stock_columns)} 只股票")

        # 分析每只股票在最近一年的数据质量
        active_columns = [0]  # 始终保留日期列（索引0）
        delisted_stocks = []

        for col_idx, col_name in stock_columns:
            # 统计该列在最近数据中的有效值数量
            valid_count = 0
            for row in recent_data:
                if col_idx < len(row) and is_valid_value(row[col_idx]):
                    valid_count += 1

            valid_ratio = valid_count / len(recent_data) if recent_data else 0

            # 如果最近一年中有效数据少于10%，认为可能已退市
            if valid_ratio < 0.1:
                stock_symbol = col_name[3:]  # 去掉'RET'前缀
                delisted_stocks.append({
                    'symbol': stock_symbol,
                    'column': col_name,
                    'column_idx': col_idx,
                    'valid_count': valid_count,
                    'valid_ratio': valid_ratio
                })
            else:
                active_columns.append(col_idx)

        print(f"\n分析结果:")
        print(f"活跃股票数量: {len(active_columns) - 1}")  # 减去日期列
        print(f"可能已退市股票数量: {len(delisted_stocks)}")

        if delisted_stocks:
            print(f"\n可能已退市的股票 (最近{recent_days}个交易日中有效数据<10%):")
            for i, stock in enumerate(delisted_stocks[:20]):  # 显示前20个
                print(f"  {stock['symbol']}: {stock['valid_count']}/{recent_days} 有效数据 ({stock['valid_ratio']:.1%})")

            if len(delisted_stocks) > 20:
                print(f"  ... 还有 {len(delisted_stocks) - 20} 只股票")

        # 创建清理后的数据
        if output_file:
            print(f"\n正在创建清理后的CSV文件...")

            # 按索引排序活跃列
            active_columns.sort()

            print(f"原始数据: {len(headers)} 列")
            print(f"清理后数据: {len(active_columns)} 列")
            print(f"移除了 {len(headers) - len(active_columns)} 列")

            # 保存清理后的数据
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # 写入新的标题行
                new_headers = [headers[i] for i in active_columns]
                writer.writerow(new_headers)

                # 写入数据行
                for row in all_rows:
                    new_row = []
                    for col_idx in active_columns:
                        if col_idx < len(row):
                            new_row.append(row[col_idx])
                        else:
                            new_row.append('')
                    writer.writerow(new_row)

            print(f"清理后的数据已保存到: {output_file}")

            # 保存已退市股票列表
            delisted_file = output_file.replace('.csv', '_delisted_stocks.txt')
            with open(delisted_file, 'w', encoding='utf-8') as f:
                f.write(f"可能已退市的股票列表 (基于最近{recent_days}个交易日数据)\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析标准: 最近{recent_days}个交易日中有效数据少于10%\n")
                f.write(f"总共识别出 {len(delisted_stocks)} 只可能已退市的股票\n\n")

                for stock in delisted_stocks:
                    f.write(f"{stock['symbol']}: {stock['valid_count']}/{recent_days} 有效数据 ({stock['valid_ratio']:.1%})\n")

            print(f"已退市股票列表已保存到: {delisted_file}")

        return {
            'active_stocks': len(active_columns) - 1,  # 减去日期列
            'delisted_stocks': len(delisted_stocks),
            'delisted_list': delisted_stocks
        }

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3:
        print("用法: python3 analyze_delisted_stocks.py <input_file> <output_file>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    print("SP500历史数据清理工具")
    print("=" * 40)

    result = analyze_delisted_stocks(input_file, output_file)

    if result:
        print(f"\n处理完成!")
        print(f"活跃股票: {result['active_stocks']}")
        print(f"已移除股票: {result['delisted_stocks']}")
    else:
        print("处理失败!")
        sys.exit(1)
