# DVC推送到Google Drive配置指南

## 🎯 目标
将DVC数据推送到您的Google Drive文件夹：`1E2rsAhpCVjusKwFR2TXhJgh7F8ng6KA-`

## 📋 前置准备

### 1. 确认DVC版本
```bash
python3 -m dvc version
```
确保DVC版本 >= 2.0

### 2. 安装Google Drive支持
```bash
pip install dvc[gdrive]
# 或者
pip install "dvc[gdrive]"
```

## 🔧 配置步骤

### 步骤1: 添加Google Drive远程存储
```bash
# 在dvc_pipeline目录下执行
cd dvc_pipeline

# 添加Google Drive远程存储
dvc remote add -d gdrive_storage gdrive://1E2rsAhpCVjusKwFR2TXhJgh7F8ng6KA-
```

### 步骤2: 配置Google Drive设置
```bash
# 设置确认滥用警告（可选，但推荐）
dvc remote modify gdrive_storage gdrive_acknowledge_abuse true

# 设置使用服务账户（如果需要）
# dvc remote modify gdrive_storage gdrive_use_service_account true
```

### 步骤3: 验证配置
```bash
# 查看远程存储配置
dvc remote list

# 查看详细配置
cat .dvc/config
```

应该看到类似输出：
```ini
[core]
    remote = gdrive_storage
[remote "gdrive_storage"]
    url = gdrive://1E2rsAhpCVjusKwFR2TXhJgh7F8ng6KA-
    gdrive_acknowledge_abuse = true
```

## 🚀 首次推送

### 步骤4: 执行推送
```bash
# 推送所有数据到Google Drive
dvc push
```

**重要**: 首次运行时会出现认证流程！

### 步骤5: Google认证流程
当执行`dvc push`时，会出现以下情况：

1. **浏览器自动打开**（或显示链接）
2. **登录Google账户**
3. **授权DVC访问Google Drive**
4. **复制授权码**（如果需要）
5. **粘贴到终端**（如果需要）

认证成功后，DVC会自动开始上传文件。

## 📊 预期结果

### 上传过程
```bash
# 示例输出
Collecting files and computing hashes in data/raw/SP500 Hsitorical Data.csv
Uploading data/raw/SP500 Hsitorical Data.csv to remote storage
100%|████████████| 91.9M/91.9M [02:15<00:00, 678kB/s]

Collecting files and computing hashes in data/processed/SP500_Historical_Data_Cleaned.csv
Uploading data/processed/SP500_Historical_Data_Cleaned.csv to remote storage
100%|████████████| 71.8M/71.8M [01:45<00:00, 682kB/s]

# ... 其他文件
```

### Google Drive中的文件结构
上传完成后，在您的Google Drive文件夹中会看到：
```
1E2rsAhpCVjusKwFR2TXhJgh7F8ng6KA-/
└── files/
    └── md5/
        ├── 45/
        │   └── 313b86eaa14b4c3d83d6a9297ae7ad
        ├── 11/
        │   └── dce4c292e4cef0f210697f7632c489
        └── 56/
            └── 030756313bb48e6584f6789b1a9d46
```

## 🔍 验证上传

### 步骤6: 验证数据完整性
```bash
# 检查远程存储状态
dvc status -r gdrive_storage

# 验证特定文件
dvc check-ignore data/processed/SP500_Historical_Data_Cleaned.csv
```

### 步骤7: 测试下载（可选）
```bash
# 备份当前数据
mv data data_backup

# 从Google Drive拉取数据
dvc pull

# 验证数据一致性
diff -r data data_backup
```

## 🛠️ 故障排除

### 常见问题1: 认证失败
```bash
# 清除认证缓存
rm -rf ~/.dvc/tmp/gdrive-user-credentials.json

# 重新认证
dvc push
```

### 常见问题2: 权限错误
确保：
- Google Drive文件夹是您拥有的
- 文件夹设置为可写
- 您有足够的Google Drive空间

### 常见问题3: 网络超时
```bash
# 设置更长的超时时间
dvc remote modify gdrive_storage timeout 300

# 或者分批上传
dvc push data/raw/SP500\ Hsitorical\ Data.csv.dvc
dvc push data/processed/SP500_Historical_Data_Cleaned.csv.dvc
```

### 常见问题4: 文件夹ID错误
验证文件夹ID是否正确：
- 在Google Drive中打开文件夹
- 查看URL中的ID部分
- 确保与配置中的ID一致

## 📈 空间使用

### 预计上传大小
```
原始数据:     ~92MB
处理后数据:   ~72MB  
重排序数据:   ~72MB
结果文件:     ~1MB
总计:         ~237MB
```

### Google Drive空间检查
```bash
# 检查Google Drive剩余空间
# 访问: https://drive.google.com/settings/storage
```

## 🔄 日常使用

### 推送新数据
```bash
# 修改参数后重新运行
echo "rebalance_freq: 42" > params.yaml
dvc repro

# 推送新结果
dvc push
```

### 拉取数据（团队协作）
```bash
# 其他团队成员
git clone your-repo
cd project/dvc_pipeline
dvc pull
```

## ⚙️ 高级配置

### 并发上传
```bash
# 设置并发上传数量
dvc remote modify gdrive_storage jobs 2
```

### 缓存设置
```bash
# 设置本地缓存策略
dvc config cache.type symlink
```

### 忽略特定文件
```bash
# 创建.dvcignore文件
echo "*.tmp" >> .dvcignore
echo "debug/" >> .dvcignore
```

## 📝 完整执行清单

### 一键执行脚本
```bash
#!/bin/bash
# google_drive_setup.sh

echo "=== DVC Google Drive 配置 ==="

# 1. 安装依赖
echo "安装Google Drive支持..."
pip install "dvc[gdrive]"

# 2. 配置远程存储
echo "配置远程存储..."
dvc remote add -d gdrive_storage gdrive://1E2rsAhpCVjusKwFR2TXhJgh7F8ng6KA-
dvc remote modify gdrive_storage gdrive_acknowledge_abuse true

# 3. 显示配置
echo "当前配置:"
dvc remote list
cat .dvc/config

# 4. 执行推送
echo "开始推送数据..."
dvc push

echo "=== 配置完成 ==="
```

### 使用方法
```bash
# 保存上述脚本为文件
chmod +x google_drive_setup.sh
./google_drive_setup.sh
```

## 🎯 成功标志

配置成功后，您应该看到：
1. ✅ DVC配置文件包含Google Drive设置
2. ✅ 认证流程顺利完成
3. ✅ 文件成功上传到Google Drive
4. ✅ `dvc status`显示所有文件已同步
5. ✅ Google Drive文件夹中出现DVC文件结构

## 💡 小贴士

- **首次上传较慢**: Google Drive API有速率限制
- **保持网络稳定**: 大文件上传需要稳定连接
- **定期清理**: 删除不需要的历史版本节省空间
- **备份认证**: 保存认证信息以避免重复认证

---

**准备好了吗？** 按照上述步骤执行，您的DVC数据很快就会安全地存储在Google Drive中！🚀
