stages:
  clean_data:
    cmd: python3 scripts/analyze_delisted_stocks.py "data/raw/SP500 Hsitorical Data.csv" data/processed/SP500_Historical_Data_Cleaned.csv
    deps:
    - data/raw/SP500 Hsitorical Data.csv
    - scripts/analyze_delisted_stocks.py
    outs:
    - data/processed/SP500_Historical_Data_Cleaned.csv
    - data/processed/SP500_Historical_Data_Cleaned_delisted_stocks.txt
    desc: "清理SP500历史数据，移除已退市股票"

  reorder_columns:
    cmd: python3 scripts/reorder_columns_by_obs.py data/processed/SP500_Historical_Data_Cleaned.csv data/processed/SP500_Historical_Data_Reordered.csv
    deps:
    - data/processed/SP500_Historical_Data_Cleaned.csv
    - scripts/reorder_columns_by_obs.py
    outs:
    - data/processed/SP500_Historical_Data_Reordered.csv
    - data/processed/SP500_Historical_Data_Reordered_column_stats.txt
    desc: "按观测数量重新排序列，时间列在最前"

  backtest:
    cmd: python3 scripts/parametrized_backtest.py data/processed/SP500_Historical_Data_Reordered.csv params.yaml
    deps:
    - data/processed/SP500_Historical_Data_Reordered.csv
    - scripts/parametrized_backtest.py
    - params.yaml
    outs:
    - results/backtest_metrics.json
    - results/detailed_results.json
    - results/performance_plot.json
    desc: "运行参数化的动态公司池投资组合回测"

metrics:
- results/backtest_metrics.json

plots:
- results/performance_plot.json:
    x: date
    y: cumulative_return
    title: "Portfolio Performance Over Time"
