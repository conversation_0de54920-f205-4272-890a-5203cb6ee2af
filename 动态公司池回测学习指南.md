# 动态公司池投资组合回测学习指南

## 🎯 核心问题

在真实市场中，公司会上市、退市、被收购，投资池是动态变化的。如何在回测中正确处理这种变化，避免**前瞻偏差**和**生存偏差**？

## 💡 解决方案：动态公司池(Dynamic Universe)

### 核心原则
> **在每个时点，只使用当时真实可用的股票进行投资决策**

## 🏗️ 框架设计思路

### 1. **时点感知的股票池构建**

```python
def get_available_stocks(self, current_idx, lookback_days=252):
    """在指定时点获取可用股票池"""
    available_stocks = []
    start_idx = max(0, current_idx - lookback_days)
    
    for stock in self.stock_columns:
        # 检查数据质量
        values = self.data[stock][start_idx:current_idx+1]
        valid_values = [v for v in values if v is not None]
        
        # 要求：1) 足够历史数据 2) 当前时点有数据
        if (len(valid_values) >= lookback_days * 0.7 and 
            self.data[stock][current_idx] is not None):
            available_stocks.append(stock)
    
    return available_stocks
```

**关键点**：
- ✅ **避免前瞻偏差**：只使用当前时点之前的信息
- ✅ **数据质量控制**：要求足够的历史数据
- ✅ **现实约束**：当前时点必须有数据才能交易

### 2. **动态再平衡策略**

```python
def equal_weight_strategy(self, rebalance_freq=21):
    """等权重策略示例"""
    for i in range(start_idx, len(self.dates)):
        # 定期再平衡
        if i == start_idx or (i - start_idx) % rebalance_freq == 0:
            # 重新构建投资池
            available_stocks = self.get_available_stocks(i)
            
            # 等权重分配
            if len(available_stocks) > 0:
                weight = 1.0 / len(available_stocks)
                current_portfolio = {stock: weight for stock in available_stocks}
        
        # 计算当期收益
        period_return = sum(weight * self.data[stock][i] 
                          for stock, weight in current_portfolio.items()
                          if self.data[stock][i] is not None)
```

**关键点**：
- 🔄 **定期重构**：投资池会随时间变化
- ⚖️ **权重调整**：新股票进入，退市股票自动移除
- 📊 **收益计算**：只计算实际可交易股票的收益

### 3. **生存偏差的自然处理**

传统问题：
- ❌ 使用"现在还存在的公司"的历史数据
- ❌ 忽略已退市公司的影响

我们的解决方案：
- ✅ **自然进入**：公司上市后自动进入投资池
- ✅ **自然退出**：公司退市后自动从投资池移除
- ✅ **真实反映**：投资池大小随时间变化

## 📊 回测结果分析

### 投资池演进
```
1980年: 213只股票 → 2023年: 518只股票
平均投资池大小: 357只股票
```

这个变化反映了：
- 📈 **市场扩张**：更多公司上市
- 🔄 **成分变化**：老公司退市，新公司进入
- 🎯 **真实性**：符合历史实际情况

### 策略表现对比

| 策略 | 年化收益率 | 年化波动率 | 夏普比率 |
|------|------------|------------|----------|
| 等权重策略 | 5.33% | 17.60% | 0.303 |
| 动量策略 | 6.05% | 23.57% | 0.257 |

**观察**：
- 动量策略收益更高但波动也更大
- 等权重策略风险调整后表现更好

## 🛠️ 代码实现要点

### 1. **数据结构设计**
```python
# 使用字典存储，便于按列访问
self.data = {
    'date': [所有日期],
    'RETAAPL': [苹果收益率序列],
    'RETMSFT': [微软收益率序列],
    # ...
}
```

### 2. **缺失数据处理**
```python
def _safe_float(self, value):
    """安全转换，处理各种缺失值标记"""
    if not value or value.strip() in ['', '.b', 'b', '.', 'nan']:
        return None
    try:
        return float(value)
    except ValueError:
        return None
```

### 3. **策略扩展框架**
```python
def momentum_strategy(self, lookback_days=63, top_n=20):
    """动量策略：选择过去表现最好的股票"""
    # 1. 获取可用股票池
    available_stocks = self.get_available_stocks(i)
    
    # 2. 计算动量得分
    momentum_scores = {}
    for stock in available_stocks:
        # 计算过去N天累积收益
        returns = [self.data[stock][j] for j in range(i-lookback_days, i)]
        momentum_scores[stock] = sum(r for r in returns if r is not None)
    
    # 3. 选择前N只股票
    selected_stocks = sorted(momentum_scores.items(), 
                           key=lambda x: x[1], reverse=True)[:top_n]
    
    # 4. 等权重分配
    return {stock: 1.0/len(selected_stocks) for stock, _ in selected_stocks}
```

## 🎓 学习要点

### 1. **时间维度思考**
- 始终问自己："在这个时点，我能知道什么信息？"
- 避免使用"未来信息"进行历史决策

### 2. **现实约束建模**
- 交易成本、流动性限制
- 数据可得性、报告延迟
- 市场准入限制

### 3. **偏差识别**
- **前瞻偏差**：使用未来信息
- **生存偏差**：只看"幸存者"
- **数据挖掘偏差**：过度拟合历史

### 4. **稳健性检验**
- 不同时期的表现
- 参数敏感性分析
- 样本外测试

## 🚀 扩展方向

### 1. **更复杂的选股策略**
```python
def value_momentum_strategy(self):
    """价值+动量多因子策略"""
    # 结合估值指标和动量指标
    pass

def risk_parity_strategy(self):
    """风险平价策略"""
    # 基于风险贡献的权重分配
    pass
```

### 2. **交易成本建模**
```python
def apply_transaction_costs(self, trades, cost_rate=0.001):
    """应用交易成本"""
    # 计算换手率和交易成本
    pass
```

### 3. **风险管理**
```python
def apply_risk_controls(self, portfolio):
    """应用风险控制"""
    # 个股权重上限、行业集中度限制等
    pass
```

## 💡 实战建议

1. **从简单开始**：先实现等权重策略
2. **逐步复杂化**：添加选股规则、风险控制
3. **多维度验证**：不同时期、不同参数
4. **关注实用性**：考虑实际交易约束

## 🎯 核心价值

这个框架的价值不在于复杂的算法，而在于：
- ✅ **正确建模**现实市场环境
- ✅ **避免常见偏差**
- ✅ **提供可扩展**的研究平台
- ✅ **培养正确**的量化思维

---

*记住：好的回测不是为了得到漂亮的数字，而是为了理解策略在真实市场中的表现！*
