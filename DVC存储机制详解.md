# DVC存储机制详解：data文件夹 vs .dvc/cache

## 🎯 核心区别

### 📁 **data文件夹**
- **作用**: 工作目录，您直接操作的文件
- **内容**: 当前版本的实际数据文件
- **可见性**: 您可以直接查看、编辑、使用这些文件

### 🗄️ **.dvc/cache文件夹**
- **作用**: 版本仓库，DVC的内部存储
- **内容**: 所有历史版本的文件副本（按哈希值组织）
- **可见性**: DVC内部管理，通常不直接操作

## 📊 实际对比

### data文件夹内容
```bash
data/
├── raw/
│   └── SP500 Hsitorical Data.csv     # 91.9MB - 原始数据
└── processed/
    ├── SP500_Historical_Data_Cleaned.csv      # 71.8MB - 清理后数据
    ├── SP500_Historical_Data_Reordered.csv    # 71.8MB - 重排序数据
    └── *.txt                                   # 各种报告文件
```

### .dvc/cache文件夹内容
```bash
.dvc/cache/files/md5/
├── 45/
│   └── 313b86eaa14b4c3d83d6a9297ae7ad    # 对应某个文件的哈希值
├── 11/
│   └── dce4c292e4cef0f210697f7632c489    # 对应另一个文件的哈希值
├── 56/
│   └── 030756313bb48e6584f6789b1a9d46    # 对应结果文件的哈希值
└── ...
```

## 🔗 文件关系映射

从`dvc.lock`文件可以看到文件与哈希值的对应关系：

### 清理后数据
```yaml
- path: data/processed/SP500_Historical_Data_Cleaned.csv
  hash: md5
  md5: 45313b86eaa14b4c3d83d6a9297ae7ad    # 对应 .dvc/cache/files/md5/45/313b86...
  size: 71768460
```

### 重排序数据
```yaml
- path: data/processed/SP500_Historical_Data_Reordered.csv
  hash: md5
  md5: 11dce4c292e4cef0f210697f7632c489    # 对应 .dvc/cache/files/md5/11/dce4c2...
  size: 71768460
```

### 结果文件
```yaml
- path: results/backtest_metrics.json
  hash: md5
  md5: 56030756313bb48e6584f6789b1a9d46    # 对应 .dvc/cache/files/md5/56/030756...
  size: 287
```

## 🔄 工作机制

### 1. **文件创建时**
```
1. 脚本生成新文件 → data/processed/new_file.csv
2. DVC计算文件哈希 → md5: abc123...
3. DVC复制到缓存 → .dvc/cache/files/md5/ab/c123...
4. DVC记录映射关系 → dvc.lock
```

### 2. **文件访问时**
```
用户访问: data/processed/file.csv
↓
DVC检查: dvc.lock中的哈希值
↓
DVC定位: .dvc/cache/files/md5/xx/yyyy...
↓
提供内容: 通过链接或复制机制
```

### 3. **版本切换时**
```
参数改变 → 重新运行管道 → 生成新文件 → 新哈希值 → 新缓存条目
旧版本文件仍在缓存中保留
```

## 💾 存储策略

### 在我们的项目中：

#### data文件夹（工作区）
```bash
$ ls -la data/processed/
-rw-r--r--  71768460  SP500_Historical_Data_Cleaned.csv
-rw-r--r--  71768460  SP500_Historical_Data_Reordered.csv
```
**总大小**: ~144MB（两个文件）

#### .dvc/cache文件夹（版本库）
```bash
$ du -sh .dvc/cache
139M    .dvc/cache
```
**实际大小**: 139MB（包含所有版本 + 元数据）

## 🎯 关键理解

### ✅ **data文件夹**
- **当前工作版本**: 您正在使用的文件
- **可以编辑**: 直接修改这些文件
- **会被覆盖**: 重新运行管道时会更新
- **用户友好**: 正常的文件路径和名称

### ✅ **.dvc/cache文件夹**
- **历史版本库**: 所有曾经存在的文件版本
- **只读存储**: 不应该直接修改
- **永久保存**: 除非手动清理
- **哈希命名**: 文件名是内容的哈希值

## 🔍 验证实验

### 查看文件inode（验证是否为硬链接）
```bash
# 查看工作文件的inode
ls -li data/processed/SP500_Historical_Data_Cleaned.csv
# 输出: 194799036 -rw-r--r--@ 1 <USER> <GROUP> 71768460 Jun 21 01:02

# 查看缓存文件的inode
ls -li .dvc/cache/files/md5/45/313b86eaa14b4c3d83d6a9297ae7ad
# 如果inode相同，说明是硬链接；如果不同，说明是独立副本
```

### 修改实验
```bash
# 1. 修改参数
echo "rebalance_freq: 42" > params.yaml

# 2. 重新运行
dvc repro

# 3. 查看缓存增长
du -sh .dvc/cache  # 应该会增加

# 4. 查看新的哈希值
cat dvc.lock | grep "md5:"  # 会看到新的哈希值
```

## 📋 总结

| 方面 | data文件夹 | .dvc/cache文件夹 |
|------|------------|------------------|
| **用途** | 工作目录 | 版本仓库 |
| **内容** | 当前版本 | 所有版本 |
| **命名** | 有意义的文件名 | 哈希值 |
| **访问** | 直接使用 | DVC内部管理 |
| **修改** | 可以编辑 | 只读 |
| **大小** | 当前文件大小 | 累积所有版本 |

**简单类比**: 
- `data/` 就像您桌面上的工作文件
- `.dvc/cache/` 就像一个自动备份系统，保存了所有历史版本

这样设计的好处是：您可以正常使用`data/`中的文件，而DVC在后台自动管理版本历史！
