#!/usr/bin/env python3
"""
检查SP500公司在被纳入指数之前的历史数据是否保存在表格中
"""

import csv
from datetime import datetime

def parse_date(date_str):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, "%d%b%Y")
    except ValueError:
        try:
            return datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            return None

def is_valid_value(value):
    """判断是否为有效数据"""
    if not value or value.strip() in ['', '.b', 'b', '.', 'nan', 'NaN']:
        return False
    try:
        float(value)
        return True
    except ValueError:
        return False

def analyze_pre_inclusion_data():
    """分析公司在被纳入SP500之前的数据"""
    
    # 一些已知的公司上市时间和SP500纳入时间的例子
    test_cases = [
        {
            'symbol': 'AAPL',
            'company': 'Apple Inc.',
            'ipo_date': '1980-12-12',
            'sp500_inclusion': '1982-11-30',  # 大概时间
            'expected_early_data': True
        },
        {
            'symbol': 'MSFT', 
            'company': 'Microsoft',
            'ipo_date': '1986-03-13',
            'sp500_inclusion': '1994-06-01',  # 大概时间
            'expected_early_data': True
        },
        {
            'symbol': 'AMZN',
            'company': 'Amazon',
            'ipo_date': '1997-05-15',
            'sp500_inclusion': '2005-11-01',  # 大概时间
            'expected_early_data': True
        },
        {
            'symbol': 'GOOGL',
            'company': 'Google/Alphabet',
            'ipo_date': '2004-08-19',
            'sp500_inclusion': '2006-03-31',
            'expected_early_data': True
        },
        {
            'symbol': 'TSLA',
            'company': 'Tesla',
            'ipo_date': '2010-06-29',
            'sp500_inclusion': '2020-12-21',
            'expected_early_data': True
        },
        {
            'symbol': 'ABNB',
            'company': 'Airbnb',
            'ipo_date': '2020-12-10',
            'sp500_inclusion': '2023-09-18',  # 假设的纳入时间
            'expected_early_data': True
        }
    ]
    
    print("检查SP500公司在被纳入指数前的历史数据")
    print("=" * 60)
    
    # 读取数据
    with open("SP500_Historical_Data_Reordered.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
        all_rows = list(reader)
    
    # 找到日期列和股票列的映射
    stock_columns = {}
    for i, header in enumerate(headers):
        if header.startswith('RET'):
            symbol = header[3:]  # 去掉RET前缀
            stock_columns[symbol] = i
    
    print(f"数据期间: {all_rows[0][0]} 到 {all_rows[-1][0]}")
    print(f"总数据行数: {len(all_rows)}")
    print()
    
    for case in test_cases:
        symbol = case['symbol']
        company = case['company']
        
        print(f"分析 {symbol} ({company})")
        print("-" * 40)
        
        if symbol not in stock_columns:
            print(f"❌ {symbol} 不在数据中")
            print()
            continue
        
        col_idx = stock_columns[symbol]
        
        # 找到第一个有效数据点
        first_valid_date = None
        first_valid_row_idx = None
        
        for row_idx, row in enumerate(all_rows):
            if col_idx < len(row) and is_valid_value(row[col_idx]):
                date_str = row[0]
                first_valid_date = parse_date(date_str)
                first_valid_row_idx = row_idx
                break
        
        if first_valid_date:
            print(f"📊 数据中首次出现: {first_valid_date.strftime('%Y-%m-%d')}")
            print(f"🏢 公司IPO时间: {case['ipo_date']}")
            print(f"📈 SP500纳入时间: {case['sp500_inclusion']}")
            
            # 比较时间
            ipo_date = datetime.strptime(case['ipo_date'], '%Y-%m-%d')
            
            if first_valid_date <= ipo_date:
                print(f"✅ 数据从IPO时就开始记录")
            else:
                days_diff = (first_valid_date - ipo_date).days
                print(f"⚠️  数据晚于IPO {days_diff} 天开始记录")
            
            # 检查数据连续性
            valid_count = 0
            total_count = 0
            for row in all_rows[first_valid_row_idx:first_valid_row_idx+252]:  # 检查第一年
                total_count += 1
                if col_idx < len(row) and is_valid_value(row[col_idx]):
                    valid_count += 1
            
            coverage = valid_count / total_count if total_count > 0 else 0
            print(f"📊 第一年数据覆盖率: {coverage:.1%}")
            
        else:
            print(f"❌ 在数据中未找到有效数据")
        
        print()
    
    # 总体分析
    print("=" * 60)
    print("总体分析结论")
    print("=" * 60)
    
    # 统计有多少公司的数据从1980年就开始
    companies_from_1980 = 0
    companies_later = 0
    
    for symbol, col_idx in stock_columns.items():
        # 检查1980年是否有数据
        has_1980_data = False
        for row in all_rows[:252]:  # 检查1980年的数据
            if col_idx < len(row) and is_valid_value(row[col_idx]):
                has_1980_data = True
                break
        
        if has_1980_data:
            companies_from_1980 += 1
        else:
            companies_later += 1
    
    print(f"从1980年就有数据的公司: {companies_from_1980} 家")
    print(f"1980年后才出现数据的公司: {companies_later} 家")
    print(f"总公司数: {len(stock_columns)} 家")
    
    print(f"\n结论:")
    print(f"• {companies_from_1980/len(stock_columns)*100:.1f}% 的公司从数据开始期就有记录")
    print(f"• {companies_later/len(stock_columns)*100:.1f}% 的公司是后来才出现在数据中")
    
    return {
        'companies_from_1980': companies_from_1980,
        'companies_later': companies_later,
        'total_companies': len(stock_columns)
    }

if __name__ == "__main__":
    analyze_pre_inclusion_data()
