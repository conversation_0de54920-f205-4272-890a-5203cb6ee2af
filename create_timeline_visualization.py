#!/usr/bin/env python3
"""
创建SP500股票上市时间的可视化时间线
"""

def create_ascii_timeline():
    """
    创建ASCII艺术风格的时间线
    """
    
    # 基于分析结果的数据
    timeline_data = [
        (1980, 227, "1980年就存在的老牌公司", "████████████████████████████████████████████"),
        (1990, 111, "1990年代科技革命开始", "██████████████████████"),
        (2000, 47, "2000年代互联网成熟期", "█████████"),
        (2010, 54, "2010年代移动互联网时代", "███████████"),
        (2020, 14, "2020年代分拆和新兴公司", "███")
    ]
    
    print("SP500股票上市时间分布可视化")
    print("=" * 60)
    print()
    
    print("时间线 (1980-2023年):")
    print("-" * 60)
    
    max_count = max(data[1] for data in timeline_data)
    
    for year, count, description, bar in timeline_data:
        percentage = count / 529 * 100
        print(f"{year}年代: {count:3d}家 ({percentage:4.1f}%) {bar}")
        print(f"         {description}")
        print()
    
    print("图例:")
    print("█ = 约10家公司")
    print()
    
    # 重要里程碑
    print("重要里程碑:")
    print("-" * 30)
    milestones = [
        ("1980", "数据开始年份，227家老牌公司"),
        ("1995", "互联网商业化开始"),
        ("2000", "互联网泡沫顶峰 (10家新上市)"),
        ("2008", "金融危机影响"),
        ("2015", "科技股繁荣 (12家新上市)"),
        ("2020", "疫情影响和新经济"),
        ("2023", "大公司分拆潮 (5家新上市)")
    ]
    
    for year, event in milestones:
        print(f"{year}: {event}")

def create_coverage_analysis():
    """
    创建数据覆盖率分析
    """
    
    print("\n" + "=" * 60)
    print("数据覆盖率分析")
    print("=" * 60)
    
    coverage_data = [
        ("90-100%", 69, "历史悠久蓝筹股", "█████████████"),
        ("70-90%", 109, "较早上市稳定公司", "██████████████████████"),
        ("50-70%", 113, "中期上市成长公司", "███████████████████████"),
        ("30-50%", 65, "较新上市公司", "█████████████"),
        ("10-30%", 59, "近期上市公司", "████████████"),
        ("0-10%", 14, "最新上市公司", "███")
    ]
    
    print("\n数据覆盖率分布:")
    print("-" * 40)
    
    for range_str, count, description, bar in coverage_data:
        percentage = count / 529 * 100
        print(f"{range_str:8s}: {count:3d}家 ({percentage:4.1f}%) {bar}")
        print(f"           {description}")
        print()

def create_recent_companies_timeline():
    """
    创建最近上市公司的详细时间线
    """
    
    print("\n" + "=" * 60)
    print("2020-2023年新上市公司详细时间线")
    print("=" * 60)
    
    recent_companies = [
        ("2020-12-11", "ABNB", "Airbnb", "共享经济平台"),
        ("2021-08-03", "BBWI", "Bath & Body Works", "从L Brands分拆"),
        ("2021-10-04", "XPRO", "Expro Group", "油田服务"),
        ("2022-01-31", "METV", "Meta Materials", "超材料技术"),
        ("2022-05-10", "BALL", "Ball Corporation", "包装材料"),
        ("2023-01-05", "GEHC", "GE HealthCare", "从通用电气分拆"),
        ("2023-05-05", "KVUE", "Kenvue", "从强生分拆"),
        ("2023-05-16", "RVTY", "Revvity", "生命科学"),
        ("2023-07-10", "EG", "Everest Group", "再保险"),
        ("2023-10-03", "VLTO", "Veralto", "从丹纳赫分拆")
    ]
    
    print("\n最新上市公司时间线:")
    print("-" * 50)
    
    current_year = None
    for date, symbol, name, description in recent_companies:
        year = date[:4]
        if year != current_year:
            print(f"\n{year}年:")
            current_year = year
        
        print(f"  {date}: {symbol:4s} - {name}")
        print(f"           {description}")

if __name__ == "__main__":
    create_ascii_timeline()
    create_coverage_analysis()
    create_recent_companies_timeline()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("• 57.1%的SP500公司是1980年后上市的")
    print("• 1990年代是科技公司上市的黄金时期")
    print("• 2020年代主要特征是大公司分拆")
    print("• 数据缺失主要反映了市场的自然演进")
    print("• 不同时期的公司代表了不同的经济发展阶段")
