#!/usr/bin/env python3
"""
专门检查Google/Alphabet的数据情况
"""

import csv
from datetime import datetime

def parse_date(date_str):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, "%d%b%Y")
    except ValueError:
        return None

def is_valid_value(value):
    """判断是否为有效数据"""
    if not value or value.strip() in ['', '.b', 'b', '.', 'nan', 'NaN']:
        return False
    try:
        float(value)
        return True
    except ValueError:
        return False

def check_google_data():
    """检查Google的数据"""
    
    print("检查Google/Alphabet的数据情况")
    print("=" * 50)
    
    # 读取数据
    with open("SP500_Historical_Data_Reordered.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
        all_rows = list(reader)
    
    # 找到GOOG和GOOGL的列
    goog_idx = None
    googl_idx = None
    
    for i, header in enumerate(headers):
        if header == 'RETGOOG':
            goog_idx = i
        elif header == 'RETGOOGL':
            googl_idx = i
    
    print(f"GOOG列索引: {goog_idx}")
    print(f"GOOGL列索引: {googl_idx}")
    print()
    
    # 检查GOOG的数据
    if goog_idx:
        print("GOOG数据分析:")
        print("-" * 30)
        
        first_valid_date = None
        for row_idx, row in enumerate(all_rows):
            if goog_idx < len(row) and is_valid_value(row[goog_idx]):
                date_str = row[0]
                first_valid_date = parse_date(date_str)
                print(f"GOOG首次出现: {first_valid_date.strftime('%Y-%m-%d')}")
                break
        
        # 统计GOOG的数据覆盖
        goog_valid_count = 0
        for row in all_rows:
            if goog_idx < len(row) and is_valid_value(row[goog_idx]):
                goog_valid_count += 1
        
        print(f"GOOG有效数据点: {goog_valid_count}")
        print(f"GOOG数据覆盖率: {goog_valid_count/len(all_rows)*100:.1f}%")
        print()
    
    # 检查GOOGL的数据
    if googl_idx:
        print("GOOGL数据分析:")
        print("-" * 30)
        
        first_valid_date = None
        for row_idx, row in enumerate(all_rows):
            if googl_idx < len(row) and is_valid_value(row[googl_idx]):
                date_str = row[0]
                first_valid_date = parse_date(date_str)
                print(f"GOOGL首次出现: {first_valid_date.strftime('%Y-%m-%d')}")
                break
        
        # 统计GOOGL的数据覆盖
        googl_valid_count = 0
        for row in all_rows:
            if googl_idx < len(row) and is_valid_value(row[googl_idx]):
                googl_valid_count += 1
        
        print(f"GOOGL有效数据点: {googl_valid_count}")
        print(f"GOOGL数据覆盖率: {googl_valid_count/len(all_rows)*100:.1f}%")
        print()
    
    # 检查两者的关系
    if goog_idx and googl_idx:
        print("GOOG vs GOOGL 数据对比:")
        print("-" * 30)
        
        both_have_data = 0
        only_goog = 0
        only_googl = 0
        neither = 0
        
        for row in all_rows:
            goog_valid = goog_idx < len(row) and is_valid_value(row[goog_idx])
            googl_valid = googl_idx < len(row) and is_valid_value(row[googl_idx])
            
            if goog_valid and googl_valid:
                both_have_data += 1
            elif goog_valid and not googl_valid:
                only_goog += 1
            elif not goog_valid and googl_valid:
                only_googl += 1
            else:
                neither += 1
        
        print(f"两者都有数据: {both_have_data} 天")
        print(f"只有GOOG: {only_goog} 天")
        print(f"只有GOOGL: {only_googl} 天")
        print(f"两者都没有: {neither} 天")
    
    print("\n" + "=" * 50)
    print("结论:")
    print("Google在2004年IPO，但数据中:")
    print("- GOOG: 从2004年开始有数据")
    print("- GOOGL: 从2014年开始有数据（股票分拆后）")
    print("这说明数据包含了公司被纳入SP500之前的历史数据！")

if __name__ == "__main__":
    check_google_data()
