#!/usr/bin/env python3
"""
分析SP500股票的上市时间分布和描述统计
"""

import csv
import sys
from collections import defaultdict
from datetime import datetime

def is_valid_value(value):
    """判断一个值是否为有效的股票收益率数据"""
    if not value or value.strip() == '':
        return False
    if value.strip() in ['.b', 'b', '.', 'nan', 'NaN', 'NULL']:
        return False
    try:
        float(value)
        return True
    except ValueError:
        return False

def parse_date(date_str):
    """解析日期字符串，返回datetime对象"""
    try:
        # 尝试解析 "ddmmmyyyy" 格式，如 "02jan1980"
        return datetime.strptime(date_str, "%d%b%Y")
    except ValueError:
        try:
            # 尝试其他可能的格式
            return datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            return None

def find_first_trading_date(csv_file):
    """
    分析每个股票的首次交易日期
    """
    
    print("正在分析股票首次交易日期...")
    try:
        # 读取CSV文件
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取标题行
            
            # 将所有数据读入内存
            all_rows = list(reader)
        
        total_rows = len(all_rows)
        print(f"数据行数: {total_rows}")
        print(f"列数: {len(headers)}")
        
        # 找到日期列的索引
        date_col_idx = 0  # 假设第一列是日期列
        
        # 获取所有股票列的索引（除了日期列）
        stock_columns = []
        for i, header in enumerate(headers):
            if i != date_col_idx and header.startswith('RET'):
                stock_columns.append((i, header))
        
        print(f"股票列数: {len(stock_columns)}")
        
        # 分析每个股票的首次交易日期
        stock_listing_info = []
        
        for col_idx, col_name in stock_columns:
            stock_symbol = col_name[3:] if col_name.startswith('RET') else col_name
            
            # 找到第一个有效数据的日期
            first_valid_date = None
            first_valid_row_idx = None
            
            for row_idx, row in enumerate(all_rows):
                if col_idx < len(row) and is_valid_value(row[col_idx]):
                    date_str = row[0]  # 日期列
                    first_valid_date = parse_date(date_str)
                    first_valid_row_idx = row_idx
                    break
            
            # 计算数据覆盖率
            valid_count = 0
            for row in all_rows:
                if col_idx < len(row) and is_valid_value(row[col_idx]):
                    valid_count += 1
            
            coverage_ratio = valid_count / total_rows if total_rows > 0 else 0
            
            stock_listing_info.append({
                'symbol': stock_symbol,
                'column': col_name,
                'first_date': first_valid_date,
                'first_row_idx': first_valid_row_idx,
                'valid_count': valid_count,
                'coverage_ratio': coverage_ratio,
                'missing_years': (total_rows - valid_count) / 252 if total_rows > 0 else 0  # 假设每年252个交易日
            })
        
        # 按首次交易日期排序
        stock_listing_info.sort(key=lambda x: x['first_date'] if x['first_date'] else datetime.max)
        
        return stock_listing_info, all_rows[0][0], all_rows[-1][0]  # 返回第一个和最后一个日期
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def analyze_listing_statistics(stock_listing_info, start_date_str, end_date_str):
    """
    分析上市时间的描述统计
    """
    
    print(f"\n=== SP500股票上市时间分析 ===")
    print(f"数据期间: {start_date_str} 到 {end_date_str}")
    print(f"总股票数: {len(stock_listing_info)}")
    
    # 统计不同时期上市的公司数量
    decade_counts = defaultdict(int)
    year_counts = defaultdict(int)
    
    # 1980年就有数据的公司（老牌公司）
    original_companies = []
    later_companies = []
    
    start_year = 1980
    
    for stock in stock_listing_info:
        if stock['first_date']:
            year = stock['first_date'].year
            decade = (year // 10) * 10
            
            decade_counts[decade] += 1
            year_counts[year] += 1
            
            if year == start_year:
                original_companies.append(stock)
            else:
                later_companies.append(stock)
        else:
            # 没有有效数据的公司
            pass
    
    print(f"\n=== 基本统计 ===")
    print(f"1980年就开始交易的公司: {len(original_companies)} ({len(original_companies)/len(stock_listing_info)*100:.1f}%)")
    print(f"1980年后上市的公司: {len(later_companies)} ({len(later_companies)/len(stock_listing_info)*100:.1f}%)")
    
    # 按年代统计
    print(f"\n=== 按年代统计上市公司数量 ===")
    for decade in sorted(decade_counts.keys()):
        count = decade_counts[decade]
        percentage = count / len(stock_listing_info) * 100
        print(f"{decade}年代: {count:3d} 家公司 ({percentage:5.1f}%)")
    
    # 最近20年的详细统计
    print(f"\n=== 2000年后各年上市公司数量 ===")
    recent_years = {year: count for year, count in year_counts.items() if year >= 2000}
    for year in sorted(recent_years.keys()):
        count = recent_years[year]
        print(f"{year}: {count:2d} 家公司")
    
    # 数据覆盖率分析
    print(f"\n=== 数据覆盖率分析 ===")
    coverage_ranges = [
        (0.9, 1.0, "90-100%"),
        (0.7, 0.9, "70-90%"),
        (0.5, 0.7, "50-70%"),
        (0.3, 0.5, "30-50%"),
        (0.1, 0.3, "10-30%"),
        (0.0, 0.1, "0-10%")
    ]
    
    for min_cov, max_cov, label in coverage_ranges:
        count = sum(1 for stock in stock_listing_info 
                   if min_cov <= stock['coverage_ratio'] < max_cov)
        percentage = count / len(stock_listing_info) * 100
        print(f"数据覆盖率 {label}: {count:3d} 家公司 ({percentage:5.1f}%)")
    
    # 最早和最晚上市的公司
    print(f"\n=== 最早上市的公司 (前10名) ===")
    for i, stock in enumerate(stock_listing_info[:10]):
        if stock['first_date']:
            print(f"{i+1:2d}. {stock['symbol']:8s}: {stock['first_date'].strftime('%Y-%m-%d')} (覆盖率: {stock['coverage_ratio']:.1%})")
    
    print(f"\n=== 最晚上市的公司 (后10名) ===")
    valid_stocks = [s for s in stock_listing_info if s['first_date']]
    for i, stock in enumerate(valid_stocks[-10:]):
        rank = len(valid_stocks) - 9 + i
        print(f"{rank:2d}. {stock['symbol']:8s}: {stock['first_date'].strftime('%Y-%m-%d')} (覆盖率: {stock['coverage_ratio']:.1%})")
    
    return {
        'total_companies': len(stock_listing_info),
        'original_companies': len(original_companies),
        'later_companies': len(later_companies),
        'decade_counts': dict(decade_counts),
        'year_counts': dict(year_counts)
    }

def save_detailed_report(stock_listing_info, output_file):
    """
    保存详细的上市时间报告
    """
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("SP500股票上市时间详细报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("股票列表 (按首次交易日期排序):\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'排名':<4} {'股票代码':<8} {'首次交易日期':<12} {'数据覆盖率':<10} {'有效观测值':<8} {'缺失年数':<8}\n")
        f.write("-" * 80 + "\n")
        
        for i, stock in enumerate(stock_listing_info):
            if stock['first_date']:
                date_str = stock['first_date'].strftime('%Y-%m-%d')
            else:
                date_str = "无数据"
            
            f.write(f"{i+1:<4} {stock['symbol']:<8} {date_str:<12} {stock['coverage_ratio']:.1%} "
                   f"{stock['valid_count']:<8} {stock['missing_years']:.1f}年\n")

if __name__ == "__main__":
    input_file = "SP500_Historical_Data_Reordered.csv"
    output_file = "SP500_Listing_Analysis.txt"
    
    print("SP500股票上市时间分析工具")
    print("=" * 40)
    
    stock_info, start_date, end_date = find_first_trading_date(input_file)
    
    if stock_info:
        stats = analyze_listing_statistics(stock_info, start_date, end_date)
        save_detailed_report(stock_info, output_file)
        print(f"\n详细报告已保存到: {output_file}")
    else:
        print("分析失败!")
